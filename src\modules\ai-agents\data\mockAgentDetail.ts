import { AgentDetailDto, TypeAgentDetailDto } from '../types';

/**
 * Mock data cho agent detail để test trang edit
 */
export const mockAgentDetail: AgentDetailDto = {
  id: 'agent-001',
  name: 'AI Assistant Pro',
  avatar: '/assets/images/avatars/agent-001.png',
  active: true,
  level: 5,
  exp: 750,
  expMax: 1000,
  model_id: 'gpt-4o',
  typeName: 'Business Assistant',
  typeId: 1,
  badge_url: '/assets/images/badges/level-5.png',
  createdAt: 1705312200000, // timestamp
  updatedAt: 1705746300000, // timestamp
  instruction: 'You are a helpful business assistant.',
  
  // Model Configuration
  modelConfig: {
    temperature: 0.7,
    top_p: 0.9,
    top_k: 40,
    max_tokens: 2000,
  },

  // Profile Configuration
  profile: {
    name: 'AI Assistant Pro',
    avatar: '/assets/images/avatars/agent-001.png',
    gender: 'female',
    dateOfBirth: '1996-01-15',
    position: 'Business Assistant',
    education: 'Master in Business Administration',
    skills: ['Business Analysis', 'Project Management', 'Customer Service', 'Data Analytics'],
    personality: ['Friendly', 'Professional', 'Helpful', 'Detail-oriented'],
    languages: ['Vietnamese', 'English'],
    country: 'Vietnam',
  },

  // Vector Store (optional)
  vectorStores: {
    id: 'vs-001',
    name: 'Business Knowledge Base',
  },
};

/**
 * Mock data cho type agent detail
 */
export const mockTypeAgentDetail: TypeAgentDetailDto = {
  id: 1,
  name: 'Business Assistant',
  description: 'Comprehensive business assistant for professional tasks',
  createdAt: 1704067200000, // timestamp
  updatedAt: 1705312200000, // timestamp
  countTool: 5,

  config: {
    hasProfile: true,
    hasResources: true,
    hasStrategy: true,
    hasOutput: true,
    hasConversion: true,
    hasMultiAgent: true,
  },

  tools: [
    {
      id: 1,
      name: 'Calculator',
      description: 'Mathematical calculations and data analysis',
    },
    {
      id: 2,
      name: 'Calendar',
      description: 'Schedule management and appointment booking',
    },
    {
      id: 3,
      name: 'Email',
      description: 'Email composition and management',
    },
    {
      id: 4,
      name: 'Document Processor',
      description: 'Document creation and editing',
    },
    {
      id: 5,
      name: 'Data Analyzer',
      description: 'Data analysis and reporting',
    },
  ],
};

/**
 * Function để lấy mock agent detail theo ID
 */
export const getMockAgentDetail = (id: string): AgentDetailDto => {
  // Trong thực tế, có thể có nhiều mock agents khác nhau
  // Hiện tại chỉ return cùng một mock data
  return {
    ...mockAgentDetail,
    id,
    name: `AI Assistant ${id.slice(-3)}`,
  };
};

/**
 * Function để lấy mock type agent detail theo ID
 */
export const getMockTypeAgentDetail = (id: number): TypeAgentDetailDto => {
  return {
    ...mockTypeAgentDetail,
    id,
  };
};
