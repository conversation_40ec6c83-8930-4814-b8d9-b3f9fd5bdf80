import React, { useMemo, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { Typography, Card, Table, Badge, Icon, ActionMenu, CollapsibleCard } from '@/shared/components/common';
import { CustomerDetailData, CustomerOrder } from './types';
import { TableColumn } from '@/shared/components/common/Table/types';
import MenuIconBar from '@/modules/components/menu-bar/MenuIconBar';
import { useDataTable, useDataTableConfig } from '@/shared/hooks/table';
import { SortDirection } from '@/shared/dto/request/query.dto';

interface CustomerOrdersProps {
  customer: CustomerDetailData;
}

// Define query params interface for orders
interface OrderQueryParams {
  page: number;
  limit: number;
  search?: string;
  sortBy?: string;
  sortDirection?: SortDirection;
  status?: string;
  paymentStatus?: string;
}

/**
 * Component hiển thị đơn hàng của khách hàng
 */
const CustomerOrders: React.FC<CustomerOrdersProps> = ({ customer }) => {
  const { t } = useTranslation('business');

  // Format currency
  const formatCurrency = useCallback((amount: number) => {
    return new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: 'VND',
    }).format(amount);
  }, []);

  // Format date
  const formatDate = useCallback((dateString: string) => {
    try {
      return new Date(dateString).toLocaleDateString('vi-VN', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
      });
    } catch {
      return dateString;
    }
  }, []);

  // Get status badge variant
  const getStatusVariant = useCallback((status: string) => {
    switch (status) {
      case 'delivered':
        return 'success';
      case 'shipped':
        return 'info';
      case 'processing':
        return 'warning';
      case 'pending':
        return 'info';
      case 'cancelled':
        return 'danger';
      default:
        return 'info';
    }
  }, []);

  // Get payment status variant
  const getPaymentStatusVariant = useCallback((status: string) => {
    switch (status) {
      case 'paid':
        return 'success';
      case 'pending':
        return 'warning';
      case 'failed':
        return 'danger';
      case 'refunded':
        return 'info';
      default:
        return 'info';
    }
  }, []);

  // Get status text
  const getStatusText = useCallback((status: string) => {
    return t(`customer.order.status.${status}`, status);
  }, [t]);

  // Get payment status text
  const getPaymentStatusText = useCallback((status: string) => {
    return t(`customer.order.paymentStatus.${status}`, status);
  }, [t]);

  // Table columns
  const columns: TableColumn<CustomerOrder>[] = useMemo(() => [
    {
      key: 'orderCode',
      title: t('customer.detail.orderCode'),
      dataIndex: 'orderCode',
      render: (value: unknown) => (
        <div className="flex items-center space-x-2">
          <Icon name="shopping-cart" size="sm" className="text-muted" />
          <Typography variant="body2" className="text-foreground font-medium">
            {String(value)}
          </Typography>
        </div>
      ),
    },
    {
      key: 'date',
      title: t('customer.detail.orderDate'),
      dataIndex: 'date',
      render: (value: unknown) => (
        <Typography variant="body2" className="text-foreground">
          {formatDate(String(value))}
        </Typography>
      ),
    },
    {
      key: 'status',
      title: t('customer.detail.orderStatus'),
      dataIndex: 'status',
      render: (value: unknown) => {
        const statusValue = String(value);
        return (
          <Badge variant={getStatusVariant(statusValue)} size="sm">
            {getStatusText(statusValue)}
          </Badge>
        );
      },
    },
    {
      key: 'paymentStatus',
      title: t('customer.detail.paymentStatus'),
      dataIndex: 'paymentStatus',
      render: (value: unknown) => {
        const paymentStatusValue = String(value);
        return (
          <Badge variant={getPaymentStatusVariant(paymentStatusValue)} size="sm">
            {getPaymentStatusText(paymentStatusValue)}
          </Badge>
        );
      },
    },
    {
      key: 'totalAmount',
      title: t('customer.detail.totalAmount'),
      dataIndex: 'totalAmount',
      render: (value: unknown) => (
        <Typography variant="body2" className="text-foreground font-medium">
          {formatCurrency(Number(value))}
        </Typography>
      ),
    },
    {
      key: 'items',
      title: t('customer.detail.items'),
      dataIndex: 'items',
      render: (value: unknown) => (
        <Typography variant="body2" className="text-muted">
          {Number(value)} {t('customer.detail.itemsUnit')}
        </Typography>
      ),
    },
    {
      key: 'actions',
      title: t('common.actions'),
      render: (_, record: CustomerOrder) => (
        <ActionMenu
          items={[
            {
              id: 'view',
              label: t('common.view'),
              icon: 'eye',
              onClick: () => handleViewOrder(record.id),
            },
            {
              id: 'edit',
              label: t('common.edit'),
              icon: 'edit',
              onClick: () => handleEditOrder(record.id),
            },
            {
              id: 'divider1',
              divider: true,
            },
            {
              id: 'cancel',
              label: t('customer.order.cancel'),
              icon: 'x',
              onClick: () => handleCancelOrder(record.id),
              disabled: record.status === 'delivered' || record.status === 'cancelled',
            },
          ]}
        />
      ),
    },
  ], [t, formatCurrency, formatDate, getStatusVariant, getStatusText, getPaymentStatusVariant, getPaymentStatusText]);

  // Action handlers
  const handleViewOrder = (orderId: string) => {
    console.log('View order:', orderId);
    // Implement view order logic
  };

  const handleEditOrder = (orderId: string) => {
    console.log('Edit order:', orderId);
    // Implement edit order logic
  };

  const handleCancelOrder = (orderId: string) => {
    console.log('Cancel order:', orderId);
    // Implement cancel order logic
  };

  // Create query params function
  const createQueryParams = (params: {
    page: number;
    pageSize: number;
    searchTerm: string;
    sortBy: string | null;
    sortDirection: SortDirection | null;
    filterValue: string | number | boolean | undefined;
  }): OrderQueryParams => ({
    page: params.page,
    limit: params.pageSize,
    search: params.searchTerm || undefined,
    sortBy: params.sortBy || undefined,
    sortDirection: params.sortDirection || undefined,
    status: params.filterValue && params.filterValue !== 'all' ? String(params.filterValue) : undefined,
  });

  // Filter options for order status
  const filterOptions = [
    { id: 'all', label: t('common.all'), icon: 'list', value: 'all' },
    { id: 'pending', label: t('customer.order.status.pending', 'Pending'), icon: 'clock', value: 'pending' },
    { id: 'processing', label: t('customer.order.status.processing', 'Processing'), icon: 'loader', value: 'processing' },
    { id: 'shipped', label: t('customer.order.status.shipped', 'Shipped'), icon: 'truck', value: 'shipped' },
    { id: 'delivered', label: t('customer.order.status.delivered', 'Delivered'), icon: 'check-circle', value: 'delivered' },
    { id: 'cancelled', label: t('customer.order.status.cancelled', 'Cancelled'), icon: 'x-circle', value: 'cancelled' },
  ];

  // Use data table hook
  const dataTable = useDataTable(
    useDataTableConfig<CustomerOrder, OrderQueryParams>({
      columns,
      filterOptions,
      createQueryParams,
    })
  );

  // Mock data - in real app, this would come from API
  const ordersData = {
    items: customer.orders || [],
    meta: {
      currentPage: 1,
      totalItems: customer.orders?.length || 0,
      totalPages: 1,
    },
  };

  return (
    <CollapsibleCard
      title={
        <div className="flex items-center justify-between w-full">
          <Typography variant="h6" className="text-foreground">
            {t('customer.detail.orders')}
          </Typography>
          <Typography variant="body2" className="text-muted">
            {customer.totalOrders} {t('customer.detail.totalOrders').toLowerCase()}
          </Typography>
        </div>
      }
      defaultOpen={false}
    >
      <div className="space-y-4">
        {/* Menu Icon Bar */}
        <MenuIconBar
          onSearch={dataTable.tableData.handleSearch}
          items={dataTable.menuItems}
          onColumnVisibilityChange={dataTable.columnVisibility.setVisibleColumns}
          columns={dataTable.columnVisibility.visibleColumns}
          showDateFilter={false}
          showColumnFilter={true}
        />

        {/* Table */}
        <Card className="overflow-hidden">
          <Table<CustomerOrder>
            columns={dataTable.columnVisibility.visibleTableColumns}
            data={ordersData.items}
            rowKey="id"
            loading={false}
            sortable={true}
            onSortChange={dataTable.tableData.handleSortChange}
            pagination={{
              current: ordersData.meta.currentPage,
              pageSize: dataTable.tableData.pageSize,
              total: ordersData.meta.totalItems,
              onChange: dataTable.tableData.handlePageChange,
              showSizeChanger: true,
              pageSizeOptions: [10, 20, 50, 100],
              showFirstLastButtons: true,
              showPageInfo: true,
            }}
          />
        </Card>
      </div>
    </CollapsibleCard>
  );
};

export default CustomerOrders;
