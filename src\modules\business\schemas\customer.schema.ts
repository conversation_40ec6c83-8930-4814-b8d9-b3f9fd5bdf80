import { z } from 'zod';
import { TFunction } from 'i18next';
import { UserConvertCustomerSortField, SortDirection } from '../types/customer.types';

/**
 * Schema validation cho form khách hàng với đa ngôn ngữ
 */
export const createCustomerFormSchema = (t: TFunction) => z.object({
  name: z
    .string()
    .min(1, t('validation:required', { field: t('business:customer.form.name') }))
    .min(2, t('validation:minLength', { field: t('business:customer.form.name'), length: 2 }))
    .max(100, t('validation:maxLength', { field: t('business:customer.form.name'), length: 100 })),
  email: z
    .string()
    .min(1, t('validation:required', { field: t('business:customer.form.email') }))
    .email(t('validation:email')),
  phone: z
    .string()
    .min(1, t('validation:required', { field: t('business:customer.form.phone') }))
    .regex(/^[0-9+\-\s()]+$/, t('validation:phone'))
    .min(10, t('validation:minLength', { field: t('business:customer.form.phone'), length: 10 })),
  tags: z
    .union([
      z.string(),
      z.array(z.string())
    ])
    .optional()
    .transform((val) => {
      if (Array.isArray(val)) {
        return val.join(', ');
      }
      return val || '';
    }),
});

/**
 * Schema validation cho query khách hàng
 */
export const customerQuerySchema = z.object({
  page: z.coerce.number().int().positive().optional().default(1),
  limit: z.coerce.number().int().positive().optional().default(10),
  search: z.string().optional(),
  sortBy: z.string().optional(),
  sortDirection: z.enum(['ASC', 'DESC']).optional(),
  status: z.string().optional(),
});

/**
 * Schema validation cho query khách hàng chuyển đổi
 */
export const convertCustomerQuerySchema = z.object({
  page: z.coerce.number().int().positive().optional().default(1),
  limit: z.coerce.number().int().positive().optional().default(10),
  search: z.string().optional(),
  platform: z.string().optional(),
  agentId: z.string().optional(),
  sortBy: z.nativeEnum(UserConvertCustomerSortField).optional().default(UserConvertCustomerSortField.CREATED_AT),
  sortDirection: z.nativeEnum(SortDirection).optional().default(SortDirection.DESC),
});

/**
 * Schema validation cho cập nhật khách hàng
 */
export const createUpdateCustomerSchema = (t: TFunction) => z.object({
  name: z
    .string()
    .min(2, t('validation:minLength', { field: t('business:customer.form.name'), length: 2 }))
    .max(100, t('validation:maxLength', { field: t('business:customer.form.name'), length: 100 }))
    .optional(),
  email: z
    .string()
    .email(t('validation:email'))
    .optional(),
  phone: z
    .string()
    .regex(/^[0-9+\-\s()]+$/, t('validation:phone'))
    .min(10, t('validation:minLength', { field: t('business:customer.form.phone'), length: 10 }))
    .optional(),
  tags: z
    .union([
      z.string(),
      z.array(z.string())
    ])
    .optional()
    .transform((val) => {
      if (Array.isArray(val)) {
        return val.join(', ');
      }
      return val || '';
    }),
});

/**
 * Type definitions
 */
export type CustomerFormValues = z.infer<ReturnType<typeof createCustomerFormSchema>>;
export type CustomerQueryParams = z.infer<typeof customerQuerySchema>;
export type ConvertCustomerQueryParams = z.infer<typeof convertCustomerQuerySchema>;
export type UpdateCustomerValues = z.infer<ReturnType<typeof createUpdateCustomerSchema>>;
