import React, { useState, useRef, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Button, Container, Icon, Input, Loading, EmptyState } from '@/shared/components/common';
import AgentConfigAccordionProvider from '../contexts/AgentConfigAccordionContext.tsx';
import {
  ConvertConfig,
  IntegrationConfig,
  IntegrationConfigData,
  ModelConfig,
  MultiAgentConfig,
  ProfileConfig,
  ResponseConfig,
  StrategyConfig,
} from '../components/agent-config';
import {
  AgentConfigData,
  ConvertData,
  ModelConfigData,
  MultiAgentConfigData,
  ProfileData,
  ResponseData,
  StrategyData,
} from '../types';
import { useGetAgentDetailWithService, useUpdateAgentWithService } from '../hooks/useAgentService';
import {
  mapAgentDetailDtoToAgentConfigData,
  mapAgentConfigDataToUpdateAgentDto,
  getComponentVisibilityFromAgentDetail,
} from '../utils/agent-data-mappers';
import { NotificationUtil } from '@/shared/utils/notification';

/**
 * Trang chỉnh sửa Agent - giống AgentCreatePage nhưng bỏ màn chọn type
 */
const AgentEditPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();

  // State để lưu dữ liệu cấu hình agent
  const [agentData, setAgentData] = useState<
    | (AgentConfigData & {
        // Các cờ để xác định component nào sẽ được hiển thị
        hasProfile?: boolean;
        hasModel?: boolean;
        hasIntegrations?: boolean;
        hasStrategy?: boolean;
        hasConvert?: boolean;
        hasResponse?: boolean;
        hasMultiAgent?: boolean;
      })
    | null
  >(null);

  // Hooks để lấy và cập nhật dữ liệu agent
  const {
    data: agentDetailResponse,
    isLoading: isLoadingAgent,
    error: agentError,
  } = useGetAgentDetailWithService(id || '');

  const updateAgentMutation = useUpdateAgentWithService();

  // Effect để load dữ liệu agent khi có response từ API
  useEffect(() => {
    if (agentDetailResponse?.result) {
      const agentDetail = agentDetailResponse.result;
      const configData = mapAgentDetailDtoToAgentConfigData(agentDetail);
      const componentVisibility = getComponentVisibilityFromAgentDetail(agentDetail);

      setAgentData({
        ...configData,
        ...componentVisibility,
      });
    }
  }, [agentDetailResponse]);

  // Tham chiếu đến input file ẩn
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Xử lý khi nhấp vào overlay để tải lên avatar
  const handleAvatarUpload = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  // Xử lý khi người dùng đã chọn file ảnh
  const handleAvatarFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file && agentData) {
      const imageUrl = URL.createObjectURL(file);
      setAgentData(prev =>
        prev
          ? {
              ...prev,
              avatar: imageUrl,
            }
          : null
      );
    }
  };

  // Xử lý khi thay đổi input (tên agent)
  const handleInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = event.target;
    if (agentData) {
      setAgentData(prev =>
        prev
          ? {
              ...prev,
              [name]: value,
            }
          : null
      );
    }
  };

  // Xử lý khi cập nhật profile
  const handleProfileUpdate = (profileData: ProfileData) => {
    if (agentData) {
      setAgentData(prev =>
        prev
          ? {
              ...prev,
              profile: profileData,
            }
          : null
      );
    }
  };

  // Xử lý khi cập nhật cấu hình model
  const handleModelConfigUpdate = (modelConfigData: ModelConfigData) => {
    if (agentData) {
      setAgentData(prev =>
        prev
          ? {
              ...prev,
              modelConfig: modelConfigData,
            }
          : null
      );
    }
  };

  // Xử lý khi cập nhật tích hợp
  const handleIntegrationsUpdate = (integrationsData: IntegrationConfigData) => {
    if (agentData) {
      setAgentData(prev =>
        prev
          ? {
              ...prev,
              integrations: integrationsData,
            }
          : null
      );
    }
  };

  // Xử lý khi cập nhật chiến lược
  const handleStrategyUpdate = (strategyData: StrategyData) => {
    if (agentData) {
      setAgentData(prev =>
        prev
          ? {
              ...prev,
              strategy: strategyData,
            }
          : null
      );
    }
  };

  // Xử lý khi cập nhật tài nguyên phản hồi
  const handleResponseUpdate = (responseData: ResponseData) => {
    if (agentData) {
      setAgentData(prev =>
        prev
          ? {
              ...prev,
              response: responseData,
            }
          : null
      );
    }
  };

  // Xử lý khi cập nhật dữ liệu chuyển đổi
  const handleConvertUpdate = (convertData: ConvertData) => {
    if (agentData) {
      setAgentData(prev =>
        prev
          ? {
              ...prev,
              convert: convertData,
            }
          : null
      );
    }
  };

  // Xử lý khi cập nhật multi-agent
  const handleMultiAgentUpdate = (multiAgentData: MultiAgentConfigData) => {
    if (agentData) {
      setAgentData(prev =>
        prev
          ? {
              ...prev,
              multiAgent: multiAgentData,
            }
          : null
      );
    }
  };

  // Xử lý khi lưu cấu hình agent
  const handleSaveAgentConfig = async () => {
    if (!id || !agentData) {
      NotificationUtil.error({
        message: 'Không thể lưu: Thiếu thông tin agent',
      });
      return;
    }

    try {
      const updateData = mapAgentConfigDataToUpdateAgentDto(agentData);

      await updateAgentMutation.mutateAsync({
        id,
        data: updateData,
      });

      NotificationUtil.success({
        message: 'Đã cập nhật agent thành công!',
      });

      // Quay về trang chi tiết agent
      navigate(`/ai-agents/${id}`);
    } catch (error) {
      console.error('Error updating agent:', error);
      NotificationUtil.error({
        message: 'Có lỗi xảy ra khi cập nhật agent. Vui lòng thử lại.',
      });
    }
  };

  // Xử lý khi quay lại trang chi tiết
  const handleBackToDetail = () => {
    navigate(`/ai-agents/${id}`);
  };

  // Hiển thị loading
  if (isLoadingAgent) {
    return (
      <Container>
        <div className="flex justify-center items-center h-64">
          <Loading size="lg" />
        </div>
      </Container>
    );
  }

  // Hiển thị lỗi
  if (agentError || !id) {
    return (
      <Container>
        <div className="py-6">
          <EmptyState
            icon="alert-circle"
            title="Không thể tải thông tin agent"
            description="Có lỗi xảy ra khi tải thông tin agent. Vui lòng thử lại."
            actions={
              <Button variant="primary" onClick={() => navigate('/ai-agents')}>
                Quay lại danh sách
              </Button>
            }
          />
        </div>
      </Container>
    );
  }

  // Hiển thị loading khi chưa có dữ liệu agent
  if (!agentData) {
    return (
      <Container>
        <div className="flex justify-center items-center h-64">
          <Loading size="lg" />
        </div>
      </Container>
    );
  }

  return (
    <AgentConfigAccordionProvider defaultOpenComponent="profile">
      <div>
        <div className="py-4 sm:py-6">
          {/* Header */}
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-4 sm:mb-6 gap-4 sm:gap-0">
            <div className="flex items-center gap-2">
              <Button variant="ghost" onClick={handleBackToDetail} className="p-1">
                <Icon name="arrow-left" size="sm" />
              </Button>
              <h1 className="text-xl sm:text-2xl font-bold">Chỉnh sửa Agent</h1>
            </div>
            <div className="flex space-x-2 sm:space-x-4 w-full sm:w-auto">
              <Button
                variant="outline"
                onClick={handleBackToDetail}
                className="flex-1 sm:flex-none"
                leftIcon={<Icon name="x" size="sm" />}
              >
                Hủy
              </Button>
              <Button
                variant="primary"
                onClick={handleSaveAgentConfig}
                isLoading={updateAgentMutation.isPending}
                className="flex-1 sm:flex-none"
                leftIcon={<Icon name="edit" size="sm" />}
              >
                Lưu thay đổi
              </Button>
            </div>
          </div>

          {/* Avatar và tên agent */}
          <div className="mb-4 sm:mb-6 p-3 sm:p-4 rounded-lg">
            <div className="flex flex-col items-center justify-center">
              {/* Avatar */}
              <div className="relative w-36 h-36 rounded-full overflow-hidden bg-gradient-to-br from-red-500 to-orange-300 flex items-center justify-center border-4 border-white group cursor-pointer mb-4">
                {agentData.avatar ? (
                  <img src={agentData.avatar} alt="Avatar" className="w-full h-full object-cover" />
                ) : (
                  <div className="text-white">
                    <Icon name="user" size="xl" />
                  </div>
                )}

                <div
                  className="absolute bottom-0 left-0 right-0 h-1/4 bg-black bg-opacity-0 group-hover:bg-opacity-50 transition-all duration-300 flex items-center justify-center"
                  onClick={handleAvatarUpload}
                >
                  <div className="opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                    <Icon name="upload" size="md" className="text-white" />
                  </div>
                </div>

                <input
                  ref={fileInputRef}
                  type="file"
                  accept="image/*"
                  className="hidden"
                  onChange={handleAvatarFileChange}
                />
              </div>

              {/* Input tên */}
              <div className="w-full max-w-xs">
                <Input
                  id="name"
                  name="name"
                  value={agentData.name}
                  onChange={handleInputChange}
                  placeholder="Nhập tên agent"
                  className="w-full text-center"
                  required
                />
              </div>
            </div>
          </div>

          {/* Các component cấu hình */}
          <div className="space-y-4 sm:space-y-6">
            {/* Model Config */}
            {agentData.hasModel && (
              <ModelConfig initialData={agentData.modelConfig} onSave={handleModelConfigUpdate} />
            )}

            {/* Profile Config */}
            {agentData.hasProfile && (
              <ProfileConfig initialData={agentData.profile} onSave={handleProfileUpdate} />
            )}

            {/* Integration Config */}
            {agentData.hasIntegrations && (
              <IntegrationConfig
                initialData={agentData.integrations}
                onSave={handleIntegrationsUpdate}
              />
            )}

            {/* Strategy Config */}
            {agentData.hasStrategy && (
              <StrategyConfig initialData={agentData.strategy} onSave={handleStrategyUpdate} />
            )}

            {/* Convert Config */}
            {agentData.hasConvert && (
              <ConvertConfig initialData={agentData.convert} onSave={handleConvertUpdate} />
            )}

            {/* Response Config */}
            {agentData.hasResponse && (
              <ResponseConfig initialData={agentData.response} onSave={handleResponseUpdate} />
            )}

            {/* Multi Agent Config */}
            {agentData.hasMultiAgent && (
              <MultiAgentConfig
                initialData={agentData.multiAgent}
                onSave={handleMultiAgentUpdate}
                availableAgents={[]} // Sẽ cần load từ API
              />
            )}
          </div>
        </div>
      </div>
    </AgentConfigAccordionProvider>
  );
};

export default AgentEditPage;
