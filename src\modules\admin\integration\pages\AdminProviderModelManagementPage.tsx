import React, { useState, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { Card, ConfirmDeleteModal, Typography, Button, Icon,  Pagination } from '@/shared/components/common';

import { SortDirection } from '@/shared/dto/request/query.dto';
import MenuIconBar, { ColumnVisibility } from '@/modules/components/menu-bar/MenuIconBar';
import SlideInForm from '@/shared/components/common/SlideInForm';
import { ActiveFilters } from '@/modules/components/filters';

import useSlideForm from '@/shared/hooks/useSlideForm';

import {
  useAdminProviderModels,
  useCreateAdminProviderModel,
  useUpdateAdminProviderModel,
  useDeleteAdminProviderModel,
} from '../provider-model/hooks';
import {
  ProviderModelListItem,
  ProviderModel,
  CreateProviderModelDto,
  UpdateProviderModelDto,
  getProviderDisplayName,
} from '../provider-model/types';
import AdminProviderModelForm from '../components/AdminProviderModelForm';
import ProviderAvatar from '../components/ProviderAvatar';

/**
 * Trang quản lý Admin Provider Model
 */
const AdminProviderModelManagementPage: React.FC = () => {
  const { t } = useTranslation(['admin', 'common']);

  // State cho các modal/form
  const [providerModelToEdit, setProviderModelToEdit] = useState<ProviderModel | null>(null);
  const [providerModelToDelete, setProviderModelToDelete] = useState<ProviderModelListItem | null>(null);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [loadingDetailId, setLoadingDetailId] = useState<string | null>(null);

  // Hooks cho slide forms
  const {
    isVisible: isCreateFormVisible,
    showForm: showCreateForm,
    hideForm: hideCreateForm,
  } = useSlideForm();

  const {
    isVisible: isEditFormVisible,
    showForm: showEditForm,
    hideForm: hideEditForm,
  } = useSlideForm();

  // State cho column visibility
  const [visibleColumns, setVisibleColumns] = useState<ColumnVisibility[]>([]);

  // State cho table data
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize] = useState(10);
  const [searchTerm, setSearchTerm] = useState('');
  const [sortBy, setSortBy] = useState<string | null>('createdAt');
  const [sortDirection, setSortDirection] = useState<SortDirection>(SortDirection.DESC);

  // API call để lấy danh sách provider models
  const queryParams = {
    page: currentPage,
    limit: pageSize,
    search: searchTerm || undefined,
    sortBy: sortBy || undefined,
    sortDirection: sortDirection,
  };

  const {
    data: providerModelsResponse,
    isLoading,
    error,
    refetch
  } = useAdminProviderModels(queryParams);

  const providerModels = providerModelsResponse?.items || [];
  const totalItems = providerModelsResponse?.meta?.totalItems || 0;

  // Mutations
  const createProviderModelMutation = useCreateAdminProviderModel();
  const updateProviderModelMutation = useUpdateAdminProviderModel();
  const deleteProviderModelMutation = useDeleteAdminProviderModel();

  // Handlers cho card actions
  const handleEditProviderModel = useCallback(async (providerModel: ProviderModelListItem) => {
    try {
      setLoadingDetailId(providerModel.id);
      const { AdminProviderModelService } = await import('../provider-model/services');
      const response = await AdminProviderModelService.getProviderModel(providerModel.id);
      setProviderModelToEdit(response);
      showEditForm();
    } catch (error) {
      console.error('Error fetching provider model:', error);
    } finally {
      setLoadingDetailId(null);
    }
  }, [showEditForm]);

  const handleDeleteProviderModel = useCallback((providerModel: ProviderModelListItem) => {
    setProviderModelToDelete(providerModel);
    setShowDeleteConfirm(true);
  }, []);

  // Handlers cho search và filter
  const handleSearch = useCallback((term: string) => {
    setSearchTerm(term);
    setCurrentPage(1); // Reset về trang đầu khi search
  }, []);

  const handleClearSearch = useCallback(() => {
    setSearchTerm('');
    setCurrentPage(1);
  }, []);

  const handleClearSort = useCallback(() => {
    setSortBy(null);
    setSortDirection(SortDirection.DESC);
  }, []);

  const handleClearAll = useCallback(() => {
    setSearchTerm('');
    setSortBy(null);
    setSortDirection(SortDirection.DESC);
    setCurrentPage(1);
  }, []);

  // Xử lý xác nhận xóa
  const handleConfirmDelete = useCallback(async () => {
    if (!providerModelToDelete) return;

    try {
      await deleteProviderModelMutation.mutateAsync(providerModelToDelete.id);
      setShowDeleteConfirm(false);
      setProviderModelToDelete(null);
      // Refetch data sau khi xóa thành công
      refetch();
    } catch (error) {
      console.error('Error deleting provider model:', error);
    }
  }, [providerModelToDelete, deleteProviderModelMutation, refetch]);

  const handleCancelDelete = useCallback(() => {
    setShowDeleteConfirm(false);
    setProviderModelToDelete(null);
  }, []);

  // Xử lý submit form tạo mới
  const handleSubmitCreateProviderModel = useCallback(
    async (values: CreateProviderModelDto | UpdateProviderModelDto) => {
      try {
        setIsSubmitting(true);
        const createData = values as CreateProviderModelDto;

        await createProviderModelMutation.mutateAsync(createData);
        hideCreateForm();
        // Refetch data sau khi tạo thành công
        refetch();
      } catch (error) {
        console.error('Error creating provider model:', error);
      } finally {
        setIsSubmitting(false);
      }
    },
    [createProviderModelMutation, hideCreateForm, refetch]
  );

  // Xử lý submit form chỉnh sửa
  const handleSubmitEditProviderModel = useCallback(
    async (values: CreateProviderModelDto | UpdateProviderModelDto) => {
      if (!providerModelToEdit) return;

      try {
        setIsSubmitting(true);
        const updateData = values as UpdateProviderModelDto;

        await updateProviderModelMutation.mutateAsync({
          id: providerModelToEdit.id,
          data: updateData,
        });
        hideEditForm();
        setProviderModelToEdit(null);
        // Refetch data sau khi cập nhật thành công
        refetch();
      } catch (error) {
        console.error('Error updating provider model:', error);
      } finally {
        setIsSubmitting(false);
      }
    },
    [providerModelToEdit, updateProviderModelMutation, hideEditForm, refetch]
  );

  // Xử lý đóng form chỉnh sửa
  const handleCloseEditForm = useCallback(() => {
    hideEditForm();
    setTimeout(() => {
      setProviderModelToEdit(null);
    }, 300);
  }, [hideEditForm]);

  // Xử lý thay đổi hiển thị cột
  const handleColumnVisibilityChange = useCallback((columns: ColumnVisibility[]) => {
    setVisibleColumns(columns);
  }, []);

  return (
    <div>
      {/* Header */}
  
      <div className="space-y-6">

        {/* SlideInForm cho form tạo mới */}
        <SlideInForm isVisible={isCreateFormVisible}>
          <AdminProviderModelForm
            onSubmit={handleSubmitCreateProviderModel}
            onCancel={hideCreateForm}
            isSubmitting={isSubmitting}
          />
        </SlideInForm>

        {/* SlideInForm cho form chỉnh sửa */}
        <SlideInForm isVisible={isEditFormVisible}>
          {providerModelToEdit && (
            <AdminProviderModelForm
              initialData={providerModelToEdit}
              onSubmit={handleSubmitEditProviderModel}
              onCancel={handleCloseEditForm}
              isSubmitting={isSubmitting}
            />
          )}
        </SlideInForm>

        {/* Controls */}
        <div>
          <MenuIconBar
            onSearch={handleSearch}
            onAdd={() => showCreateForm()}
            items={[
              {
                id: 'all',
                label: t('common:all'),
                icon: 'list',
                onClick: () => '',
              },
            ]}
            onColumnVisibilityChange={handleColumnVisibilityChange}
            columns={visibleColumns}
            showDateFilter={false}
            showColumnFilter={true}
          />

          <ActiveFilters
            searchTerm={searchTerm}
            onClearSearch={handleClearSearch}
            sortBy={sortBy}
            sortDirection={sortDirection}
            onClearSort={handleClearSort}
            onClearAll={handleClearAll}
          />
        </div>

        {/* Provider Model Cards */}
        {error ? (
          <Card>
            <div className="p-12 text-center">
              <Icon name="circle-alert" size="xl" className="mx-auto text-red-400 dark:text-red-600 mb-4" />
              <Typography variant="h6" className="text-red-500 dark:text-red-400 mb-2">
                {t('admin:integration.provider.error.title', 'Lỗi tải dữ liệu')}
              </Typography>
              <Typography variant="body2" className="text-gray-400 dark:text-gray-500 mb-6">
                {t('admin:integration.provider.error.description', 'Có lỗi xảy ra khi tải danh sách Provider Model. Vui lòng thử lại.')}
              </Typography>
              <Button variant="outline" onClick={() => refetch()}>
                <Icon name="refresh-cw" size="sm" className="mr-2" />
                {t('common:retry', 'Thử lại')}
              </Button>
            </div>
          </Card>
        ) : isLoading ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {Array.from({ length: 6 }).map((_, index) => (
              <Card key={index} className="animate-pulse">
                <div className="p-6">
                  <div className="flex items-center gap-4 mb-4">
                    <div className="w-12 h-12 bg-gray-200 dark:bg-gray-700 rounded-lg"></div>
                    <div className="flex-1">
                      <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded mb-2"></div>
                      <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded w-2/3"></div>
                    </div>
                  </div>
                  <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded w-1/2"></div>
                </div>
              </Card>
            ))}
          </div>
        ) : providerModels.length === 0 ? (
          <Card>
            <div className="p-12 text-center">
              <Icon name="robot" size="xl" className="mx-auto text-gray-400 dark:text-gray-600 mb-4" />
              <Typography variant="h6" className="text-gray-500 dark:text-gray-400 mb-2">
                {t('admin:integration.provider.empty.title', 'Chưa có Provider Model nào')}
              </Typography>
              <Typography variant="body2" className="text-gray-400 dark:text-gray-500 mb-6">
                {t('admin:integration.provider.empty.description', 'Thêm Provider Model để bắt đầu sử dụng.')}
              </Typography>
              <Button variant="primary" onClick={() => showCreateForm()}>
                <Icon name="plus" size="sm" className="mr-2" />
                {t('admin:integration.provider.actions.create', 'Tạo Provider Model')}
              </Button>
            </div>
          </Card>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {providerModels.map((providerModel) => (
              <Card key={providerModel.id} className="hover:shadow-lg transition-all duration-300">
                <div className="p-6">
                  <div className="flex items-center gap-4 mb-4">
                    {/* Provider Avatar */}
                    <ProviderAvatar
                      type={providerModel.type}
                      avatar={providerModel.avatar}
                      size="md"
                    />

                    {/* Provider Info */}
                    <div className="flex-1 min-w-0">
                      <Typography variant="h6" className="font-semibold text-gray-900 dark:text-white truncate">
                        {providerModel.name}
                      </Typography>
                      <Typography variant="body2" className="text-gray-600 dark:text-gray-400">
                        {getProviderDisplayName(providerModel.type)}
                      </Typography>
                    </div>

                    {/* Actions */}
                    <div className="flex items-center gap-2">
                      <button
                        onClick={() => handleEditProviderModel(providerModel)}
                        disabled={loadingDetailId === providerModel.id}
                        className="p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                        title={t('admin:integration.provider.actions.edit', 'Chỉnh sửa')}
                      >
                        {loadingDetailId === providerModel.id ? (
                          <Icon name="loader-circle" size="sm" className="text-gray-600 dark:text-gray-400 animate-spin" />
                        ) : (
                          <Icon name="edit" size="sm" className="text-gray-600 dark:text-gray-400" />
                        )}
                      </button>
                      <button
                        onClick={() => handleDeleteProviderModel(providerModel)}
                        className="p-2 rounded-lg hover:bg-red-50 dark:hover:bg-red-900/20 transition-colors"
                        title={t('admin:integration.provider.actions.delete', 'Xóa')}
                      >
                        <Icon name="trash" size="sm" className="text-red-500 dark:text-red-400" />
                      </button>
                    </div>
                  </div>

                  {/* Creation Date */}
                  <div className="text-sm text-gray-500 dark:text-gray-400">
                    {t('common:createdAt', 'Tạo lúc')}: {new Date(Number(providerModel.createdAt)).toLocaleDateString('vi-VN')}
                  </div>
                </div>
              </Card>
            ))}
          </div>
        )}

        {/* Pagination */}
        {!error && !isLoading && providerModels.length > 0 && totalItems > pageSize && (
          <div className="flex justify-center mt-6">
            <Pagination
              currentPage={currentPage}
              totalPages={Math.ceil(totalItems / pageSize)}
              onPageChange={setCurrentPage}
              showItemsPerPageSelector={false}
              variant="simple"
            />
          </div>
        )}
      </div>

      {/* Modal xác nhận xóa */}
      <ConfirmDeleteModal
        isOpen={showDeleteConfirm}
        onClose={handleCancelDelete}
        onConfirm={handleConfirmDelete}
        title={t('admin:integration.provider.confirmations.deleteTitle', 'Xác nhận xóa')}
        message={t('admin:integration.provider.confirmations.delete', 'Bạn có chắc chắn muốn xóa provider model này?')}
        itemName={providerModelToDelete?.name}
      />
    </div>
  );
};

export default AdminProviderModelManagementPage;